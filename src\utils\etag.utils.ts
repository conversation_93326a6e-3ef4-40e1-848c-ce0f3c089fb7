import { Request } from 'express'
import { EtagPreconditionFail } from './error.utils.js'

export const RESOURCE_DETECTED_ERROR = 'Resource detected'
export const RESOURCE_DOES_NOT_EXIST = 'Resource does not exist'
export const NO_RESOURCE_MATCH = 'No resources matched your etag precondition'
export const NO_IF_MATCH_ETAG = 'Request did not include ETag headers "If-Match" or "If-None-Match"'
export const MISSING_ETAG_INFO = 'Could not determine etag headers for this request.'

export function checkModificationConditions(req: Request, etagHash: string | undefined, created: boolean, required = true, allowStateResourceWithoutEtag = false): void {
  if (!required) {
    return
  }

  const recordAlreadyExists = !created
  const etagHeaders = getEtagInfo(req)

  // For state resources, allow PUT requests without etag headers as per xAPI spec
  if (allowStateResourceWithoutEtag && req.method === 'PUT' && !etagHeaders.ifMatch && !etagHeaders.ifNoneMatch) {
    return
  }

  if (!etagHeaders.ifMatch && !etagHeaders.ifNoneMatch && req.method == 'PUT' && !recordAlreadyExists) {
    throw new EtagPreconditionFail(MISSING_ETAG_INFO)
  }

  const hasIfMatch = etagHeaders.ifMatch !== undefined
  const hasIfNoneMatch = etagHeaders.ifNoneMatch !== undefined

  const isPutRequest = req.method === 'PUT'

  //removing "&& missingIfMatch && missingIfNoneMatch" from condition to meet XAPI spec
  if (isPutRequest && recordAlreadyExists ) {
    //throw new ConflictError(`A document matching your query already exists, but the request did not include ETag headers. If you would like to override the document, provide the following header:: IF-Match: "${etagHash}"`)
    throw new EtagPreconditionFail(NO_IF_MATCH_ETAG)
  }

  /**
   * Check against the If-None-Match condition.
   * 
   * We should only preform this check if the request has provided a header
   * here and if the record itself already exists.
   * 
   * If the record doesn't exist, then there's no match and this check is satisfied etc.
   */
  if (hasIfNoneMatch && recordAlreadyExists && etagHash) {
    // Only check if the content already exists. If it did not already exist it should pass
    if (etagHeaders.ifNoneMatch === '*' || etagHeaders.ifNoneMatch?.includes(etagHash)) {
      throw new EtagPreconditionFail(RESOURCE_DETECTED_ERROR)
    }
  }

  /**
   * Check against the If-Match condition.
   * 
   * It's unlikely that this will be checked along with the If-None-Match condition,
   * but we should still honor that weird use case.
   */
  if (hasIfMatch) {
    // We only created a record if the provided query didn't match anything
    if (created) {
      throw new EtagPreconditionFail(RESOURCE_DOES_NOT_EXIST)
    }

    const wildcardProvided = etagHeaders.ifMatch === '*'
    const matchedInclusively = etagHash ? etagHeaders.ifMatch?.includes(etagHash) : false

    if (!(wildcardProvided || matchedInclusively)) {
      throw new EtagPreconditionFail(NO_RESOURCE_MATCH)
    }
  }
}

function getEtagInfo(req: Request) {
  return {
    ifMatch: req.get('HTTP_IF_MATCH') ?? req.get('if_match') ?? req.get('if-match'),
    ifNoneMatch: req.get('HTTP_IF_NONE_MATCH') ?? req.get('if_none_match') ?? req.get('if-none-match')
  }
}