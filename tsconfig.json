{"compilerOptions": {"experimentalDecorators": true, "target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./build", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["types", "node_modules/@types"], "types": ["node", "mocha"]}, "exclude": ["src/**/*.spec.ts", "tools/"], "ts-node": {"files": true, "esm": true}}