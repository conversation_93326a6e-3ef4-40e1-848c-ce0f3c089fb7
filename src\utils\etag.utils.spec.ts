import { expect } from 'chai'
import { NO_RESOURCE_MATCH, RESOURCE_DETECTED_ERROR, RESOURCE_DOES_NOT_EXIST, checkModificationConditions } from './etag.utils.js'
import { EtagPreconditionFail } from './error.utils.js'
import httpMocks from 'node-mocks-http'
import { fail } from 'assert'

describe('ETag Utils', () => {
  describe('Check Modification Conditions', () => {

    it('should return void if match not required', () => {
      const mock = httpMocks.createMocks({})
      expect(checkModificationConditions(mock.req, undefined, false, false)).to.equal(void 0)
    })

    it('should throw a conflict error when the record is being updated and no match header is provided', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      try {
        checkModificationConditions(mock.req, 'hash', false)
        fail('Should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
      }
    })

    // Updated for XApi 2.0 compliance
    it('should pass when updating and if none match is a wildcard', () => {
      const mock = httpMocks.createMocks({ method: 'PUT', headers: { 'if-none-match': '*' } })
      console.log()
      try {
        checkModificationConditions(mock.req, 'hash', false)
        
        fail('should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(RESOURCE_DETECTED_ERROR)
      }
    })

    it('should throw an error when updating and etag hash if found in if none match header', () => {
      const mock = httpMocks.createMocks({ method: 'PUT', headers: { 'if-none-match': 'hash' } })
      try {
        checkModificationConditions(mock.req, 'hash', false)
        fail('should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(RESOURCE_DETECTED_ERROR)
      }
    })

    it('should throw an error when if-match header is provided and the record did not previously exist', () => {
      const mock = httpMocks.createMocks({ method: 'POST', headers: { 'if-match': '*' } })
      try {
        checkModificationConditions(mock.req, 'hash', true)
        fail('should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(RESOURCE_DOES_NOT_EXIST)
      }
    })

    it('should throw an error when if-match header does not match internal etag hash', () => {
      const mock = httpMocks.createMocks({ method: 'POST', headers: { 'if-match': 'abcd' } })
      try {
        checkModificationConditions(mock.req, 'hash', false)
        fail('should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(NO_RESOURCE_MATCH)
      }
    })

    it('should allow PUT requests without etag headers for state resources (new resource)', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      expect(checkModificationConditions(mock.req, undefined, true, true, true)).to.equal(void 0)
    })

    it('should allow PUT requests without etag headers for state resources (existing resource)', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      expect(checkModificationConditions(mock.req, 'hash', false, true, true)).to.equal(void 0)
    })

    it('should still enforce etag validation when allowStateResourceWithoutEtag is false', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      try {
        checkModificationConditions(mock.req, 'hash', false, true, false)
        fail('should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
      }
    })
  })
})
